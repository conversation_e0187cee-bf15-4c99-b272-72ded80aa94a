"""
管理员相关数据模型
对应FastAdmin数据库中的管理员表结构
"""

from sqlalchemy import Column, Integer, String, BigInteger, ForeignKey
from sqlalchemy.orm import relationship
from ..core.database import Base


class Admin(Base):
    """管理员表 - 对应 fa_admin"""
    __tablename__ = "fa_admin"

    # 注意：数据库中id是int(10) UNSIGNED，但SQLAlchemy的Integer可以处理
    id = Column(Integer, primary_key=True, index=True, comment="管理员ID")
    username = Column(String(20), default="", comment="用户名")  # 移除unique约束，避免冲突
    nickname = Column(String(50), default="", comment="昵称")
    password = Column(String(32), default="", comment="密码")
    salt = Column(String(30), default="", comment="密码盐")
    avatar = Column(String(255), default="", comment="头像")
    email = Column(String(100), default="", comment="电子邮箱")
    mobile = Column(String(11), default="", comment="手机号码")
    loginfailure = Column(Integer, default=0, comment="失败次数")
    logintime = Column(BigInteger, comment="登录时间")  # bigint(16)
    loginip = Column(String(50), comment="登录IP")
    createtime = Column(BigInteger, comment="创建时间")  # bigint(16)
    updatetime = Column(BigInteger, comment="更新时间")  # bigint(16)
    token = Column(String(59), default="", comment="Session标识")
    status = Column(String(30), default="normal", comment="状态")
    store_id = Column(Integer, ForeignKey("fa_store.id"), comment="所属门店ID")

    # 关联关系
    store = relationship("Store", back_populates="admins")
    stock_ins = relationship("StockIn", foreign_keys="StockIn.operator_id", back_populates="operator")
    stock_outs = relationship("StockOut", foreign_keys="StockOut.operator_id", back_populates="operator")
    stock_returns = relationship("StockReturn", foreign_keys="StockReturn.operator_id", back_populates="operator")
    inventory_checks = relationship("InventoryCheck", foreign_keys="InventoryCheck.operator_id", back_populates="operator")
    inventory_check_items = relationship("InventoryCheckItem", foreign_keys="InventoryCheckItem.check_user_id", back_populates="check_user")
    recyclings = relationship("Recycling", foreign_keys="Recycling.operator_id", back_populates="operator")
    transfers = relationship("StoreTransfer", foreign_keys="StoreTransfer.admin_id", back_populates="admin")
    audited_transfers = relationship("StoreTransfer", foreign_keys="StoreTransfer.audit_id", back_populates="auditor")


class AdminLog(Base):
    """管理员日志表 - 对应 fa_admin_log"""
    __tablename__ = "fa_admin_log"

    id = Column(Integer, primary_key=True, index=True, comment="日志ID")
    admin_id = Column(Integer, default=0, comment="管理员ID")
    username = Column(String(30), default="", comment="管理员名字")
    url = Column(String(1500), default="", comment="操作页面")
    title = Column(String(100), default="", comment="日志标题")
    content = Column(String(1000), comment="内容")
    ip = Column(String(50), default="", comment="IP")
    useragent = Column(String(255), default="", comment="User-Agent")
    createtime = Column(BigInteger, comment="操作时间")