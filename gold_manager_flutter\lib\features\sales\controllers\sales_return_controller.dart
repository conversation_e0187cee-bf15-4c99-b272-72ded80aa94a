import 'package:get/get.dart';
import '../../../core/utils/logger_service.dart';
import '../../../models/sales/sales_return_model.dart';
import '../services/sales_return_service.dart';

/// 销售退货控制器
class SalesReturnController extends GetxController {
  late final SalesReturnService _salesReturnService;

  // 响应式状态
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxList<SalesReturn> salesReturnList = <SalesReturn>[].obs;
  final Rx<SalesReturn?> currentSalesReturn = Rx<SalesReturn?>(null);
  
  // 分页相关
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalCount = 0.obs;
  final RxBool hasMore = true.obs;
  final int pageSize = 20;
  
  // 筛选条件
  final RxMap<String, dynamic> filters = <String, dynamic>{}.obs;

  @override
  void onInit() {
    super.onInit();
    _salesReturnService = Get.find<SalesReturnService>();
    fetchSalesReturnList(refresh: true);
  }

  /// 获取销售退货列表
  Future<void> fetchSalesReturnList({bool refresh = false}) async {
    try {
      if (refresh) {
        currentPage.value = 1;
        hasMore.value = true;
        salesReturnList.clear();
      }

      if (!hasMore.value) return;

      isLoading.value = true;
      errorMessage.value = '';

      LoggerService.d('获取销售退货列表 - 页码: ${currentPage.value}, 筛选条件: $filters');

      final response = await _salesReturnService.getSalesReturnListWithPagination(
        page: currentPage.value,
        limit: pageSize,
        filters: Map<String, dynamic>.from(filters),
      );

      final List<SalesReturn> newItems = response['items'] as List<SalesReturn>;
      totalCount.value = response['total_count'] as int;
      totalPages.value = response['total_pages'] as int;

      if (refresh) {
        salesReturnList.assignAll(newItems);
      } else {
        salesReturnList.addAll(newItems);
      }

      // 更新分页状态
      hasMore.value = currentPage.value < totalPages.value;
      if (hasMore.value) {
        currentPage.value++;
      }

      LoggerService.d('销售退货列表获取成功 - 当前页: ${currentPage.value - 1}, 总页数: ${totalPages.value}, 总数量: ${totalCount.value}');

    } catch (e) {
      LoggerService.e('获取销售退货列表失败', e);
      errorMessage.value = e.toString();
      
      // 网络错误时不显示错误提示，保持现有数据
      if (!e.toString().contains('SocketException') && 
          !e.toString().contains('TimeoutException') &&
          !e.toString().contains('Connection refused')) {
        Get.snackbar('错误', '获取销售退货列表失败: $e');
      }
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取销售退货详情
  Future<void> fetchSalesReturnDetail(int id) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      LoggerService.d('获取销售退货详情 - ID: $id');

      final salesReturn = await _salesReturnService.getSalesReturnDetail(id);
      currentSalesReturn.value = salesReturn;

      LoggerService.d('销售退货详情获取成功 - 退货单号: ${salesReturn.returnNo}');

    } catch (e) {
      LoggerService.e('获取销售退货详情失败', e);
      errorMessage.value = e.toString();
      Get.snackbar('错误', '获取销售退货详情失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 创建销售退货单
  Future<bool> createSalesReturn(SalesReturn salesReturn) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      LoggerService.d('创建销售退货单 - 退货单号: ${salesReturn.returnNo}');

      final result = await _salesReturnService.createSalesReturn(salesReturn);
      
      if (result) {
        LoggerService.d('销售退货单创建成功');
        // 刷新列表
        await fetchSalesReturnList(refresh: true);
        return true;
      } else {
        errorMessage.value = '创建销售退货单失败';
        return false;
      }

    } catch (e) {
      LoggerService.e('创建销售退货单失败', e);
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 更新销售退货单
  Future<bool> updateSalesReturn(SalesReturn salesReturn) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      LoggerService.d('更新销售退货单 - ID: ${salesReturn.id}');

      final result = await _salesReturnService.updateSalesReturn(salesReturn);
      
      if (result) {
        LoggerService.d('销售退货单更新成功');
        // 更新当前退货单
        currentSalesReturn.value = salesReturn;
        // 刷新列表
        await fetchSalesReturnList(refresh: true);
        return true;
      } else {
        errorMessage.value = '更新销售退货单失败';
        return false;
      }

    } catch (e) {
      LoggerService.e('更新销售退货单失败', e);
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 删除销售退货单
  Future<bool> deleteSalesReturn(int id) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      LoggerService.d('删除销售退货单 - ID: $id');

      final result = await _salesReturnService.deleteSalesReturn(id);
      
      if (result) {
        LoggerService.d('销售退货单删除成功');
        // 从列表中移除
        salesReturnList.removeWhere((item) => item.id == id);
        return true;
      } else {
        errorMessage.value = '删除销售退货单失败';
        return false;
      }

    } catch (e) {
      LoggerService.e('删除销售退货单失败', e);
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 审核销售退货单
  Future<bool> approveSalesReturn(int id, String remark) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      LoggerService.d('审核销售退货单 - ID: $id, 备注: $remark');

      final result = await _salesReturnService.approveSalesReturn(id, remark);
      
      if (result) {
        LoggerService.d('销售退货单审核成功');
        // 刷新列表
        await fetchSalesReturnList(refresh: true);
        return true;
      } else {
        errorMessage.value = '审核销售退货单失败';
        return false;
      }

    } catch (e) {
      LoggerService.e('审核销售退货单失败', e);
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 取消销售退货单
  Future<bool> cancelSalesReturn(int id, String reason) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      LoggerService.d('取消销售退货单 - ID: $id, 原因: $reason');

      final result = await _salesReturnService.cancelSalesReturn(id, reason);
      
      if (result) {
        LoggerService.d('销售退货单取消成功');
        // 刷新列表
        await fetchSalesReturnList(refresh: true);
        return true;
      } else {
        errorMessage.value = '取消销售退货单失败';
        return false;
      }

    } catch (e) {
      LoggerService.e('取消销售退货单失败', e);
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 设置筛选条件
  void setFilter(String key, dynamic value) {
    if (value == null || (value is String && value.isEmpty)) {
      filters.remove(key);
    } else {
      filters[key] = value;
    }
    LoggerService.d('设置筛选条件 - $key: $value');
  }

  /// 清除所有筛选条件
  void clearFilters() {
    filters.clear();
    LoggerService.d('清除所有筛选条件');
  }

  /// 应用筛选条件
  void applyFilters() {
    LoggerService.d('应用筛选条件: $filters');
    fetchSalesReturnList(refresh: true);
  }

  /// 刷新数据
  @override
  Future<void> refresh() async {
    await fetchSalesReturnList(refresh: true);
  }

  /// 重置状态
  void resetState() {
    isLoading.value = false;
    errorMessage.value = '';
    salesReturnList.clear();
    currentSalesReturn.value = null;
    currentPage.value = 1;
    totalPages.value = 1;
    totalCount.value = 0;
    hasMore.value = true;
    filters.clear();
  }

  @override
  void onClose() {
    resetState();
    super.onClose();
  }
}