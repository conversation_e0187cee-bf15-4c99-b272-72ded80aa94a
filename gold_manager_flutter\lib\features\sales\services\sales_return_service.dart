import 'package:get/get.dart';
import '../../../core/services/api_service.dart';
import '../../../core/utils/logger_service.dart';
import '../../../core/utils/api_response_handler.dart';
import '../../../models/sales/sales_return_model.dart';

/// 销售退货服务
class SalesReturnService extends GetxService {
  late final ApiService _apiService;
  
  @override
  void onInit() {
    super.onInit();
    _apiService = Get.find<ApiService>();
  }

  /// 获取销售退货列表（带分页信息）
  Future<Map<String, dynamic>> getSalesReturnListWithPagination({
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final Map<String, dynamic> params = {
        'page': page,
        'page_size': limit,
      };

      // 添加筛选条件
      if (filters != null) {
        params.addAll(filters);
      }

      LoggerService.d('请求销售退货列表参数: $params');
      LoggerService.d('请求URL: /api/v1/sales/returns, 页码: $page, 每页: $limit');

      final response = await _apiService.get('/api/v1/sales/returns', queryParameters: params);
      
      LoggerService.d('API响应状态: ${response.statusCode}, 响应数据类型: ${response.data.runtimeType}');

      // 处理响应数据，提取分页信息
      final responseData = response.data;
      List<SalesReturn> items = [];
      int totalCount = 0;
      int totalPages = 1;

      if (responseData is Map<String, dynamic>) {
        // 提取分页信息
        if (responseData.containsKey('pagination')) {
          final pagination = responseData['pagination'] as Map<String, dynamic>?;
          if (pagination != null) {
            totalCount = pagination['total'] ?? 0;
            totalPages = pagination['pages'] ?? 1;
          }
        }
        
        // 如果没有pagination字段，尝试直接查找
        if (totalCount == 0) {
          totalCount = responseData['total'] ?? 0;
          totalPages = responseData['total_pages'] ?? responseData['pages'] ?? 1;
        }
        
        // 提取数据列表
        items = ApiResponseHandler.handleListResponse<SalesReturn>(
          responseData,
          (json) => SalesReturn.fromJson(json),
          errorContext: '获取销售退货列表',
        );
      } else {
        // 如果响应直接是数组，则使用默认分页信息
        items = ApiResponseHandler.handleListResponse<SalesReturn>(
          responseData,
          (json) => SalesReturn.fromJson(json),
          errorContext: '获取销售退货列表',
        );
        totalCount = items.length;
        totalPages = (totalCount / limit).ceil();
      }

      // 计算总页数（如果API没有返回正确值）
      if (totalPages <= 1 && totalCount > limit) {
        totalPages = (totalCount / limit).ceil();
      }

      LoggerService.d('最终解析结果: items=${items.length}, totalCount=$totalCount, totalPages=$totalPages, page=$page');
      
      // 如果数据为空，记录详细信息用于调试
      if (items.isEmpty) {
        LoggerService.w('第 $page 页数据为空! 响应数据: ${responseData.toString().substring(0, responseData.toString().length > 500 ? 500 : responseData.toString().length)}...');
      }

      return {
        'items': items,
        'total_count': totalCount,
        'total_pages': totalPages,
        'current_page': page,
        'page_size': limit,
      };
      
    } catch (e) {
      LoggerService.e('获取销售退货列表失败', e);
      
      // 如果是网络错误，返回空数据而不是抛出异常
      if (e.toString().contains('SocketException') || 
          e.toString().contains('TimeoutException') ||
          e.toString().contains('Connection refused')) {
        LoggerService.w('网络连接问题，返回空退货列表');
        return {
          'items': <SalesReturn>[],
          'total_count': 0,
          'total_pages': 1,
          'current_page': page,
          'page_size': limit,
        };
      }
      
      rethrow;
    }
  }

  /// 获取销售退货详情
  Future<SalesReturn> getSalesReturnDetail(int id) async {
    try {
      LoggerService.d('请求销售退货详情: $id');

      final response = await _apiService.get('/api/v1/sales/returns/$id');

      return ApiResponseHandler.handleObjectResponse<SalesReturn>(
        response.data,
        (json) => SalesReturn.fromJson(json),
        errorContext: '获取销售退货详情',
      )!;
      
    } catch (e) {
      LoggerService.e('获取销售退货详情失败', e);
      rethrow;
    }
  }

  /// 创建销售退货单
  Future<bool> createSalesReturn(SalesReturn salesReturn) async {
    try {
      LoggerService.d('创建销售退货单: ${salesReturn.returnNo}');

      final response = await _apiService.post(
        '/api/v1/sales/returns',
        data: salesReturn.toJson(),
      );

      if (response.data['success'] == true) {
        LoggerService.d('销售退货单创建成功');
        return true;
      } else {
        LoggerService.e('销售退货单创建失败: ${response.data['message']}');
        return false;
      }
      
    } catch (e) {
      LoggerService.e('创建销售退货单失败', e);
      rethrow;
    }
  }

  /// 更新销售退货单
  Future<bool> updateSalesReturn(SalesReturn salesReturn) async {
    try {
      LoggerService.d('更新销售退货单: ${salesReturn.id}');

      final response = await _apiService.put(
        '/api/v1/sales/returns/${salesReturn.id}',
        data: salesReturn.toJson(),
      );

      if (response.data['success'] == true) {
        LoggerService.d('销售退货单更新成功');
        return true;
      } else {
        LoggerService.e('销售退货单更新失败: ${response.data['message']}');
        return false;
      }
      
    } catch (e) {
      LoggerService.e('更新销售退货单失败', e);
      rethrow;
    }
  }

  /// 删除销售退货单
  Future<bool> deleteSalesReturn(int id) async {
    try {
      LoggerService.d('删除销售退货单: $id');

      final response = await _apiService.delete('/api/v1/sales/returns/$id');

      if (response.data['success'] == true) {
        LoggerService.d('销售退货单删除成功');
        return true;
      } else {
        LoggerService.e('销售退货单删除失败: ${response.data['message']}');
        return false;
      }
      
    } catch (e) {
      LoggerService.e('删除销售退货单失败', e);
      rethrow;
    }
  }

  /// 审核销售退货单
  Future<bool> approveSalesReturn(int id, String remark) async {
    try {
      LoggerService.d('审核销售退货单: $id, 备注: $remark');

      final response = await _apiService.post(
        '/api/v1/sales/returns/$id/approve',
        data: {'remark': remark},
      );

      if (response.data['success'] == true) {
        LoggerService.d('销售退货单审核成功');
        return true;
      } else {
        LoggerService.e('销售退货单审核失败: ${response.data['message']}');
        return false;
      }
      
    } catch (e) {
      LoggerService.e('审核销售退货单失败', e);
      rethrow;
    }
  }

  /// 取消销售退货单
  Future<bool> cancelSalesReturn(int id, String reason) async {
    try {
      LoggerService.d('取消销售退货单: $id, 原因: $reason');

      final response = await _apiService.post(
        '/api/v1/sales/returns/$id/cancel',
        data: {'reason': reason},
      );

      if (response.data['success'] == true) {
        LoggerService.d('销售退货单取消成功');
        return true;
      } else {
        LoggerService.e('销售退货单取消失败: ${response.data['message']}');
        return false;
      }
      
    } catch (e) {
      LoggerService.e('取消销售退货单失败', e);
      rethrow;
    }
  }

  /// 获取销售退货统计信息
  Future<Map<String, dynamic>> getSalesReturnStatistics({
    DateTime? startDate,
    DateTime? endDate,
    int? storeId,
    Map<String, dynamic>? additionalFilters,
  }) async {
    try {
      final Map<String, dynamic> params = {};

      if (startDate != null) {
        params['start_date'] = startDate.toIso8601String().split('T')[0];
      }

      if (endDate != null) {
        params['end_date'] = endDate.toIso8601String().split('T')[0];
      }

      if (storeId != null && storeId > 0) {
        params['store_id'] = storeId;
      }

      if (additionalFilters != null) {
        params.addAll(additionalFilters);
      }

      LoggerService.d('请求销售退货统计参数: $params');

      final response = await _apiService.get('/api/v1/sales/returns/statistics', queryParameters: params);

      if (response.data['success'] == true) {
        return response.data['data'] ?? {};
      } else {
        throw Exception(response.data['message'] ?? '获取销售退货统计失败');
      }
      
    } catch (e) {
      LoggerService.e('获取销售退货统计失败', e);
      
      // 如果是网络错误，返回空统计数据
      if (e.toString().contains('SocketException') || 
          e.toString().contains('TimeoutException') ||
          e.toString().contains('Connection refused')) {
        LoggerService.w('网络连接问题，返回空统计数据');
        return {};
      }
      
      rethrow;
    }
  }

  /// 导出销售退货数据
  Future<String> exportSalesReturnData({
    DateTime? startDate,
    DateTime? endDate,
    int? storeId,
    String format = 'excel',
  }) async {
    try {
      final Map<String, dynamic> params = {
        'format': format,
      };

      if (startDate != null) {
        params['start_date'] = startDate.toIso8601String().split('T')[0];
      }

      if (endDate != null) {
        params['end_date'] = endDate.toIso8601String().split('T')[0];
      }

      if (storeId != null && storeId > 0) {
        params['store_id'] = storeId;
      }

      LoggerService.d('导出销售退货数据参数: $params');

      final response = await _apiService.get('/api/v1/sales/returns/export', queryParameters: params);

      if (response.data['success'] == true) {
        return response.data['data']['download_url'] ?? '';
      } else {
        throw Exception(response.data['message'] ?? '导出数据失败');
      }
    } catch (e) {
      LoggerService.e('导出销售退货数据失败', e);
      rethrow;
    }
  }
}